# ویت‌پرس چیست؟ {#what-is-vitepress}

ویت‌پرس یک [تولید کننده سایت ایستا](https://en.wikipedia.org/wiki/Static_site_generator) (SSG) است که برای ساخت وب‌سایت‌های سریع و محتوا محور طراحی شده است. به طور خلاصه، ویت‌پرس محتوای منبع شما که به زبان [Markdown](https://en.wikipedia.org/wiki/Markdown) نوشته شده است را گرفته، یک تم بر روی آن اعمال می‌کند و صفحات HTML ایستا تولید می‌کند که به راحتی در هر جایی قابل استقرار هستند.

<div class="tip custom-block" style="padding-top: 8px">

فقط می‌خواهید آن را امتحان کنید؟ به [شروع سریع](./getting-started) بروید.

</div>

## موارد استفاده {#use-cases}

- **مستندسازی**

  ویت‌پرس با یک تم پیش‌فرض طراحی شده برای مستندات فنی ارائه می‌شود. این صفحه‌ای که اکنون در حال خواندن آن هستید و همچنین مستندات [Vite](https://vitejs.dev/)، [Rollup](https://rollupjs.org/)، [Pinia](https://pinia.vuejs.org/)، [VueUse](https://vueuse.org/)، [Vitest](https://vitest.dev/)، [D3](https://d3js.org/)، [UnoCSS](https://unocss.dev/)، [Iconify](https://iconify.design/) و [بسیاری دیگر](https://www.vuetelescope.com/explore?framework.slug=vitepress) با استفاده از ویت‌پرس ساخته شده‌اند.

  [مستندات رسمی Vue.js](https://vuejs.org/) نیز بر پایه ویت‌پرس ساخته شده است، اما از یک تم سفارشی که بین چندین ترجمه مشترک است استفاده می‌کند.

- **وبلاگ‌ها، نمونه کارها و سایت‌های بازاریابی**

  ویت‌پرس از [تم‌های کاملاً سفارشی](./custom-theme) پشتیبانی می‌کند، با تجربه توسعه مشابه یک برنامه استاندارد Vite + Vue. با ساختن بر روی Vite، این امکان وجود دارد که مستقیماً از پلاگین‌های Vite از اکوسیستم غنی آن استفاده کنید. علاوه بر این، ویت‌پرس API‌های انعطاف‌پذیری برای [بارگذاری داده](./data-loading) (محلی یا از راه دور) و [تولید پویا مسیرها](./routing#dynamic-routes) ارائه می‌دهد. شما می‌توانید تقریباً هر چیزی را بسازید به شرطی که داده‌ها در زمان ساخت تعیین شوند.

  وبلاگ رسمی [Vue.js](https://blog.vuejs.org/) یک وبلاگ ساده است که صفحه فهرست خود را بر اساس محتوای محلی تولید می‌کند.

## تجربه توسعه دهنده {#developer-experience}

ویت‌پرس هدف ارائه یک تجربه عالی برای توسعه دهنده (DX) هنگام کار با محتوای Markdown را دارد.

- **[قدرت گرفته از Vite:](https://vitejs.dev/)** شروع سرور فوری، با بازتاب ویرایش‌ها به صورت آنی (<100ms) بدون بارگذاری مجدد صفحه.

- **[افزونه‌های داخلی Markdown:](./markdown)** استفاده از Frontmatter، جداول، syntax highlighting... هرچه که بخواهید. ویت‌پرس به ویژه ویژگی‌های پیشرفته زیادی برای کار با بلوک‌های کد فراهم می‌کند، که آن را برای مستندات فنی بسیار مناسب می‌کند.

- **[Markdown بهبود یافته با Vue:](./using-vue)** هر صفحه Markdown نیز یک [کامپوننت تک فایل Vue](https://vuejs.org/guide/scaling-up/sfc.html) است، به لطف سازگاری ۱۰۰٪ سینتکسی قالب Vue با HTML. شما می‌توانید از ویژگی‌های قالب‌بندی Vue یا کامپوننت‌های وارد شده Vue برای ایجاد تعامل در محتوای ایستا خود استفاده کنید.

## عملکرد {#performance}

بر خلاف بسیاری از SSG‌های سنتی که هر ناوبری منجر به بارگذاری کامل صفحه می‌شود، یک وب‌سایت تولید شده توسط ویت‌پرس در بازدید اولیه HTML ایستا را سرو می‌کند، اما برای ناوبری‌های بعدی در سایت به یک [برنامه تک صفحه‌ای](https://en.wikipedia.org/wiki/Single-page_application) (SPA) تبدیل می‌شود. به نظر ما، این مدل برای عملکرد بهترین تعادل را فراهم می‌کند:

- **بارگذاری اولیه سریع**

  بازدید اولیه از هر صفحه، HTML پیش‌پردازش شده ایستا را برای سرعت بارگذاری سریع و بهینه‌سازی SEO سرو می‌کند. سپس صفحه یک بسته JavaScript را بارگذاری می‌کند که صفحه را به یک SPA Vue تبدیل می‌کند ("hydration"). بر خلاف فرضیات رایج که hydration برای SPA کند است، این فرآیند در واقع بسیار سریع است به لطف عملکرد خام Vue 3 و بهینه‌سازی‌های کامپایلر. در [PageSpeed Insights](https://pagespeed.web.dev/report?url=https%3A%2F%2Fvitepress.dev%2F)، سایت‌های معمولی ویت‌پرس حتی در دستگاه‌های موبایل پایین‌رده با شبکه کند به امتیازهای عملکردی تقریباً کامل دست می‌یابند.

- **ناوبری سریع پس از بارگذاری**

  مهم‌تر از آن، مدل SPA منجر به تجربه کاربری بهتر **پس از** بارگذاری اولیه می‌شود. ناوبری‌های بعدی در سایت دیگر باعث بارگذاری کامل صفحه نمی‌شوند. در عوض، محتوای صفحه ورودی بارگذاری و به صورت پویا به‌روزرسانی می‌شود. ویت‌پرس همچنین به صورت خودکار تکه‌های صفحه را برای لینک‌هایی که در viewport هستند پیش‌بارگذاری (pre-fetch) می‌کند. در بیشتر موارد، ناوبری پس از بارگذاری به صورت آنی احساس می‌شود.

- **تعامل بدون جریمه**

  برای اینکه بتوانید بخش‌های پویا Vue جاسازی شده در داخل Markdown ایستا را hydrated کنید، هر صفحه Markdown به عنوان یک کامپوننت Vue پردازش و به JavaScript کامپایل می‌شود. این ممکن است غیر بهینه به نظر برسد، اما کامپایلر Vue به اندازه کافی هوشمند است که بخش‌های ایستا و پویا را جدا کند، هزینه hydration و اندازه محموله را به حداقل برساند. برای بارگذاری اولیه صفحه، بخش‌های ایستا به صورت خودکار از محموله JavaScript حذف می‌شوند و در حین hydration نادیده گرفته می‌شوند.

## درباره VuePress چه؟ {#what-about-vuepress}

ویت‌پرس جانشین معنوی VuePress است. VuePress اصلی بر پایه Vue 2 و webpack بود. با Vue 3 و Vite در هسته، ویت‌پرس تجربه توسعه بهتر، عملکرد تولید بهتر، تم پیش‌فرض کامل‌تر و API سفارشی‌سازی انعطاف‌پذیرتری ارائه می‌دهد.

تفاوت API بین ویت‌پرس و VuePress عمدتاً در زمینه تم‌سازی و سفارشی‌سازی است. اگر از VuePress 1 با تم پیش‌فرض استفاده می‌کنید، باید مهاجرت به ویت‌پرس نسبتاً ساده باشد.

همچنین تلاش‌هایی برای VuePress 2 انجام شده است، که از Vue 3 و Vite با سازگاری بیشتر با VuePress 1 پشتیبانی می‌کند. با این حال، نگهداری دو SSG به صورت موازی پایدار نیست، بنابراین تیم Vue تصمیم گرفته است که در دراز مدت بر روی ویت‌پرس به عنوان SSG اصلی توصیه شده تمرکز کند.
