{"name": "vitepress", "version": "2.0.0-alpha.7", "description": "Vite & Vue powered static site generator", "keywords": ["vite", "vue", "vitepress"], "homepage": "https://vitepress.dev/", "bugs": {"url": "https://github.com/vuejs/vitepress/issues"}, "repository": "github:vuejs/vitepress", "license": "MIT", "author": "<PERSON>", "type": "module", "exports": {".": {"types": "./types/index.d.ts", "default": "./dist/node/index.js"}, "./dist/*": "./dist/*", "./package.json": "./package.json", "./client": {"types": "./client.d.ts", "default": "./dist/client/index.js"}, "./theme": {"types": "./theme.d.ts", "default": "./dist/client/theme-default/index.js"}, "./theme-without-fonts": {"types": "./theme-without-fonts.d.ts", "default": "./dist/client/theme-default/without-fonts.js"}}, "main": "dist/node/index.js", "types": "types/index.d.ts", "bin": {"vitepress": "bin/vitepress.js"}, "files": ["bin", "dist", "types", "template", "client.d.ts", "theme.d.ts", "theme-without-fonts.d.ts", "lib"], "scripts": {"dev": "rimraf dist && pnpm dev:shared && pnpm dev:start", "dev:start": "pnpm --stream '/^dev:(client|node|watch)$/'", "dev:client": "tsc --sourcemap -w --preserveWatchOutput -p src/client", "dev:node": "DEV=true pnpm build:node -w", "dev:shared": "node scripts/copyShared", "dev:watch": "node scripts/watchAndCopy", "build": "pnpm build:prepare && pnpm build:client && pnpm build:node", "build:prepare": "rimraf dist && node scripts/copyShared", "build:client": "vue-tsc --noEmit -p src/client && tsc -p src/client && node scripts/copyClient", "build:node": "tsc -p src/node --noEmit && rollup --config rollup.config.ts --configPlugin esbuild", "test": "pnpm --aggregate-output --reporter=append-only '/^test:(unit|e2e|init)$/'", "test:unit": "vitest run -r __tests__/unit", "test:unit:watch": "vitest -r __tests__/unit", "test:e2e": "pnpm test:e2e-dev && pnpm test:e2e-build", "test:e2e:site:dev": "pnpm -F=tests-e2e site:dev", "test:e2e:site:build": "pnpm -F=tests-e2e site:build", "test:e2e:site:preview": "pnpm -F=tests-e2e site:preview", "test:e2e-dev": "pnpm -F=tests-e2e test", "test:e2e-dev:watch": "pnpm -F=tests-e2e watch", "test:e2e-build": "VITE_TEST_BUILD=1 pnpm test:e2e-dev", "test:e2e-build:watch": "VITE_TEST_BUILD=1 pnpm test:e2e-dev:watch", "test:init": "pnpm -F=tests-init test", "test:init:watch": "pnpm -F=tests-init watch", "docs": "pnpm --stream '/^(docs:)?dev$/'", "docs:dev": "wait-on -d 100 dist/node/cli.js && pnpm -F=docs dev", "docs:debug": "NODE_OPTIONS='--inspect-brk' pnpm docs:dev", "docs:build": "pnpm build && pnpm docs:build:only", "docs:build:only": "pnpm -F=docs build", "docs:preview": "pnpm -F=docs preview", "docs:lunaria:build": "pnpm -F=docs lunaria:build", "docs:lunaria:open": "pnpm -F=docs lunaria:open", "format": "prettier --experimental-cli --write .", "format:fail": "prettier --experimental-cli --check .", "check": "pnpm format:fail && pnpm build && pnpm test", "changelog": "conventional-changelog -p angular -i CHANGELOG.md -s", "release": "node scripts/release.js"}, "simple-git-hooks": {"pre-commit": "pnpm lint-staged"}, "lint-staged": {"*": "prettier --experimental-cli --ignore-unknown --write"}, "dependencies": {"@docsearch/css": "^3.9.0", "@docsearch/js": "^3.9.0", "@iconify-json/simple-icons": "^1.2.40", "@shikijs/core": "^3.7.0", "@shikijs/transformers": "^3.7.0", "@shikijs/types": "^3.7.0", "@vitejs/plugin-vue": "^6.0.0", "@vue/devtools-api": "^7.7.7", "@vue/shared": "^3.5.17", "@vueuse/core": "^13.4.0", "@vueuse/integrations": "^13.4.0", "focus-trap": "^7.6.5", "mark.js": "8.11.1", "minisearch": "^7.1.2", "shiki": "^3.7.0", "vite": "^7.0.0", "vue": "^3.5.17"}, "devDependencies": {"@clack/prompts": "^1.0.0-alpha.1", "@iconify/utils": "^2.3.0", "@mdit-vue/plugin-component": "^2.1.4", "@mdit-vue/plugin-frontmatter": "^2.1.4", "@mdit-vue/plugin-headers": "^2.1.4", "@mdit-vue/plugin-sfc": "^2.1.4", "@mdit-vue/plugin-title": "^2.1.4", "@mdit-vue/plugin-toc": "^2.1.4", "@mdit-vue/shared": "^2.1.4", "@polka/compression": "^1.0.0-next.28", "@rollup/plugin-alias": "^5.1.1", "@rollup/plugin-commonjs": "^28.0.6", "@rollup/plugin-json": "^6.1.0", "@rollup/plugin-node-resolve": "^16.0.1", "@rollup/plugin-replace": "^6.0.2", "@types/cross-spawn": "^6.0.6", "@types/debug": "^4.1.12", "@types/fs-extra": "^11.0.4", "@types/lodash.template": "^4.5.3", "@types/mark.js": "^8.11.12", "@types/markdown-it": "^14.1.2", "@types/markdown-it-attrs": "^4.1.3", "@types/markdown-it-container": "^2.0.10", "@types/markdown-it-emoji": "^3.0.1", "@types/minimist": "^1.2.5", "@types/node": "^24.0.3", "@types/picomatch": "^4.0.0", "@types/postcss-prefix-selector": "^1.16.3", "@types/prompts": "^2.4.9", "chokidar": "^4.0.3", "conventional-changelog-cli": "^5.0.0", "cross-spawn": "^7.0.6", "debug": "^4.4.1", "esbuild": "^0.25.5", "execa": "^9.6.0", "fs-extra": "^11.3.0", "get-port": "^7.1.0", "gray-matter": "^4.0.3", "lint-staged": "^16.1.2", "lodash.template": "^4.5.0", "lru-cache": "^11.1.0", "markdown-it": "^14.1.0", "markdown-it-anchor": "^9.2.0", "markdown-it-async": "^2.2.0", "markdown-it-attrs": "^4.3.1", "markdown-it-container": "^4.0.0", "markdown-it-emoji": "^3.0.0", "markdown-it-mathjax3": "^4.3.2", "minimist": "^1.2.8", "nanoid": "^5.1.5", "ora": "^8.2.0", "oxc-minify": "^0.74.0", "p-map": "^7.0.3", "path-to-regexp": "^6.3.0", "picocolors": "^1.1.1", "picomatch": "^4.0.2", "package-directory": "^8.1.0", "playwright-chromium": "^1.53.1", "polka": "^1.0.0-next.28", "postcss-prefix-selector": "^2.1.1", "prettier": "^3.6.0", "prompts": "^2.4.2", "punycode": "^2.3.1", "rimraf": "^6.0.1", "rollup": "^4.44.0", "rollup-plugin-dts": "6.1.1", "rollup-plugin-esbuild": "^6.2.1", "semver": "^7.7.2", "simple-git-hooks": "^2.13.0", "sirv": "^3.0.1", "sitemap": "^8.0.0", "tinyglobby": "^0.2.14", "typescript": "^5.8.3", "vitest": "^4.0.0-beta.1", "vue-tsc": "^3.0.0-beta.3", "wait-on": "^8.0.3"}, "peerDependencies": {"markdown-it-mathjax3": "^4", "oxc-minify": "^0.74.0", "postcss": "^8"}, "peerDependenciesMeta": {"markdown-it-mathjax3": {"optional": true}, "postcss": {"optional": true}, "oxc-minify": {"optional": true}}, "packageManager": "pnpm@10.12.2", "pnpm": {"peerDependencyRules": {"ignoreMissing": ["@algolia/client-search", "search-insights", "postcss"]}, "overrides": {"ora>string-width": "^5", "vite": "npm:rolldown-vite@latest"}, "patchedDependencies": {"@types/mdurl@2.0.0": "patches/@<EMAIL>", "markdown-it-anchor@9.2.0": "patches/<EMAIL>"}, "neverBuiltDependencies": []}}