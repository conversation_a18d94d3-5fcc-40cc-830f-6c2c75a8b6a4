# Интерфейс командной строки {#command-line-interface}

## `vitepress dev` {#vitepress-dev}

Запуск сервера разработки VitePress, с использованием указанного каталога в качестве корневого. По умолчанию используется текущий каталог. Команду `dev` также можно опустить при работе в текущем каталоге.

### Использование {#usage}

```sh
# запуск в текущем каталоге, опускаем `dev`
vitepress

# запуск в подкаталоге
vitepress dev [root]
```

### Параметры {#options}

| Параметр        | Описание                                                                       |
| --------------- | ------------------------------------------------------------------------------ |
| `--open [path]` | Открытие браузера при запуске (`boolean \| string`)                            |
| `--port <port>` | Номер порта (`number`)                                                         |
| `--base <path>` | Публичный базовый путь (по умолчанию: `/`) (`string`)                          |
| `--cors`        | Включить CORS                                                                  |
| `--strictPort`  | Выйти, если указанный порт уже используется (`boolean`)                        |
| `--force`       | Заставить оптимизатор игнорировать кэш и повторно объединять файлы (`boolean`) |

## `vitepress build` {#vitepress-build}

Создание производственной сборки текущего сайта VitePress.

### Использование {#usage-1}

```sh
vitepress build [root]
```

### Параметры {#options-1}

| Параметр                       | Описание                                                                                                                            |
| ------------------------------ | ----------------------------------------------------------------------------------------------------------------------------------- |
| `--mpa` (экспериментально)     | Сборка в режиме [MPA](../guide/mpa-mode) без гидратации на стороне клиента (`boolean`)                                              |
| `--base <path>`                | Публичный базовый путь (по умолчанию: `/`) (`string`)                                                                               |
| `--target <target>`            | Транспилировать цель (по умолчанию: `"modules"`) (`string`)                                                                         |
| `--outDir <dir>`               | Выходной каталог относительно **cwd** (по умолчанию: `<root>/.vitepress/dist`) (`string`)                                           |
| `--assetsInlineLimit <number>` | Статический встроенный порог ресурса base64 в байтах (по умолчанию: `4096`) (`number`)                                              |

## `vitepress preview` {#vitepress-preview}

Локальный предварительный просмотр производственной сборки.

### Использование {#usage-2}

```sh
vitepress preview [root]
```

### Параметры {#options-2}

| Параметр        | Описание                                              |
| --------------- | ----------------------------------------------------- |
| `--base <path>` | Публичный базовый путь (по умолчанию: `/`) (`string`) |
| `--port <port>` | Номер порта (`number`)                                |

## `vitepress init` {#vitepress-init}

Запуск [Мастера настройки](../guide/getting-started#setup-wizard) в текущем каталоге.

### Использование {#usage-3}

```sh
vitepress init
```
