# Интернационализация {#internationalization}

Чтобы использовать встроенные функции i18n, необходимо создать следующую структуру каталогов:

```
docs/
├─ es/
│  ├─ foo.md
├─ ru/
│  ├─ foo.md
├─ foo.md
```

Затем в `docs/.vitepress/config.ts`:

```ts [docs/.vitepress/config.ts]
import { defineConfig } from 'vitepress'

export default defineConfig({
  // общие свойства и другие вещи верхнего уровня...

  locales: {
    root: {
      label: 'English',
      lang: 'en'
    },
    ru: {
      label: 'Русский',
      lang: 'ru', // необязательный, будет добавлен как атрибут `lang` в тег `html`
      link: '/ru/guide' // по умолчанию /ru/ -- ссылка в меню переводов на панели навигации, может быть внешней

      // другие свойства, специфичные для локали...
    }
  }
})
```

Следующие свойства могут быть переопределены для каждой локали (включая корневую):

```ts
interface LocaleSpecificConfig<ThemeConfig = any> {
  lang?: string
  dir?: string
  title?: string
  titleTemplate?: string | boolean
  description?: string
  head?: HeadConfig[] // будут объединены с существующими записями в заголовке, дублирующие метатеги будут автоматически удалены
  themeConfig?: ThemeConfig // будут неглубоко объединены, общие вещи можно поместить в запись themeConfig верхнего уровня
}
```

Подробнее о настройке текстов-заготовок темы по умолчанию см. в интерфейсе [`DefaultTheme.Config`](https://github.com/vuejs/vitepress/blob/main/types/default-theme.d.ts). Не переопределяйте `themeConfig.algolia` или `themeConfig.carbonAds` на локальном уровне. Использование многоязычного поиска описано в главе [Поиск Algolia](../reference/default-theme-search#algolia-search-i18n).

**Совет:** Конфигурационный файл можно хранить и в `docs/.vitepress/config/index.ts`. Это может помочь вам организовать работу, создав конфигурационный файл для каждой локали, а затем объединить и экспортировать их из `index.ts`.

## Отдельный каталог для каждой локали {#separate-directory-for-each-locale}

Пример многоязычной структуры:

```
docs/
├─ en/
│  ├─ foo.md
├─ es/
│  ├─ foo.md
├─ ru/
   ├─ foo.md
```

Однако по умолчанию VitePress не будет перенаправлять `/` на `/en/`. Для этого вам нужно будет настроить свой сервер. Например, в Netlify вы можете добавить файл `docs/public/_redirects` следующим образом:

```
/*  /es/:splat  302  Language=es
/*  /ru/:splat  302  Language=ru
/*  /en/:splat  302
```

**Совет:** Если вы используете описанный выше подход, вы можете использовать куки `nf_lang`, чтобы сохранить выбор языка пользователя:

```ts [docs/.vitepress/theme/index.ts]
import DefaultTheme from 'vitepress/theme'
import Layout from './Layout.vue'

export default {
  extends: DefaultTheme,
  Layout
}
```

```vue [docs/.vitepress/theme/Layout.vue]
<script setup lang="ts">
import DefaultTheme from 'vitepress/theme'
import { useData, inBrowser } from 'vitepress'
import { watchEffect } from 'vue'

const { lang } = useData()
watchEffect(() => {
  if (inBrowser) {
    document.cookie = `nf_lang=${lang.value}; expires=Mon, 1 Jan 2030 00:00:00 UTC; path=/`
  }
})
</script>

<template>
  <DefaultTheme.Layout />
</template>
```

## Поддержка RTL (экспериментально) {#rtl-support-experimental}

Для поддержки языков с написанием справа налево укажите `dir: 'rtl'` в конфиге и используйте какой-нибудь плагин RTLCSS PostCSS, например <https://github.com/MohammadYounes/rtlcss>, <https://github.com/vkalinichev/postcss-rtl> или <https://github.com/elchininet/postcss-rtlcss>. Вам нужно настроить плагин PostCSS на использование `:where([dir="ltr"])` и `:where([dir="rtl"])` в качестве префиксов, чтобы избежать проблем со спецификой CSS.
