{"$schema": "./node_modules/@lunariajs/core/config.schema.json", "repository": {"name": "vuejs/vitepress", "rootDir": "docs"}, "files": [{"location": "**/config.ts", "pattern": "@lang/@path", "type": "universal"}, {"location": "**/*.md", "pattern": "@lang/@path", "type": "universal"}], "defaultLocale": {"label": "English", "lang": "en"}, "locales": [{"label": "简体中文", "lang": "zh"}, {"label": "Português", "lang": "pt"}, {"label": "Русский", "lang": "ru"}, {"label": "Español", "lang": "es"}, {"label": "한국어", "lang": "ko"}, {"label": "فار<PERSON>ی", "lang": "fa"}], "outDir": ".vitepress/dist/_translations", "ignoreKeywords": ["lunaria-ignore"]}