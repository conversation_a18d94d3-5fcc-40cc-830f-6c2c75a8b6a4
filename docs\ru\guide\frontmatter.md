# Метаданные {#frontmatter}

## Использование {#usage}

VitePress поддерживает метаданные YAML во всех Markdown-файлах, разбирая их с помощью [gray-matter](https://github.com/jonschlinkert/gray-matter). Метаданные должны находиться в верхней части Markdown-файла (перед любыми элементами, включая теги `<script>`) и иметь вид корректного YAML, заданного между тройными пунктирными линиями. Пример:

```md
---
title: Документация с VitePress
editLink: true
---
```

Многие параметры конфигурации сайта или темы по умолчанию имеют соответствующие опции в блоке метаданных. Вы можете использовать метаданные, чтобы переопределить заданное поведение только для текущей страницы. Подробности см. в [Справочнике по настройке метаданных](../reference/frontmatter-config).

Вы также можете определить собственные метаданные, которые будут использоваться в динамических выражениях Vue на странице.

## Доступ к метаданным {#accessing-frontmatter-data}

Доступ к метаданным можно получить через специальную глобальную переменную `$frontmatter`:

Вот пример того, как можно использовать его в файле Markdown:

```md
---
title: Документация с VitePress
editLink: true
---

# {{ $frontmatter.title }}

Содержание руководства
```

Вы также можете получить доступ к метаданным текущей страницы в `<script setup>` с помощью [хелпера `useData()`](../reference/runtime-api#usedata).

## Альтернативные форматы метаданных {#alternative-frontmatter-formats}

VitePress также поддерживает синтаксис метаданных JSON, начинающийся и заканчивающийся фигурными скобками:

```json
---
{
  "title": "Веду блог как хакер",
  "editLink": true
}
---
```
