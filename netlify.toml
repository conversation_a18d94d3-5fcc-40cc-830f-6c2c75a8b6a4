[build.environment]
  NODE_VERSION = "22"
  PLAYWRIGHT_SKIP_BROWSER_DOWNLOAD = "1"

[build]
  publish = "docs/.vitepress/dist"
  command = "pnpm docs:build && pnpm docs:lunaria:build"

[[headers]]
  for = "/assets/*"
  [headers.values]
    cache-control = '''
    max-age=31536000,
    immutable'''

[[headers]]
  for = "/_translations/*"
  [headers.values]
    x-robots-tag = "noindex"

[[redirects]]
  from = "https://vitepress.vuejs.org/*"
  to = "https://vitepress.dev/:splat"
  force = true

[[redirects]]
  from = "/guide/"
  to = "/guide/getting-started"

[[redirects]]
  from = "/llms.md"
  status = 301
  to = "/llms.txt"

[[redirects]]
  from = "/llms-full.md"
  status = 301
  to = "/llms-full.txt"
