# طرح بندی {#layout}

می‌توانید طرح صفحه را با تنظیم گزینه `layout` در [frontmatter](./frontmatter-config) صفحه انتخاب کنید. سه گزینه طرح وجود دارد، `doc`، `page` و `home`. اگر هیچ چیز مشخص نشده باشد، صفحه به عنوان صفحه `doc` در نظر گرفته می‌شود.

```yaml
---
layout: doc
---
```

## طرح Doc {#doc-layout}

گزینه `doc` طرح پیش‌فرض است و تمام محتوای Markdown را به "نمایشگاه" درست می‌کند. این با پوشاندن کل محتوا در داخل کلاس css `vp-doc` کار می‌کند و استایل‌های لازم را بر روی عناصر زیرش اعمال می‌کند.

تقریباً همه عناصر عمومی مانند `p` یا `h2` استایل‌های خاصی دارند. بنابراین، به یاد داشته باشید که اگر HTML سفارشی‌ای درون محتوای Markdown اضافه کنید، این استایل‌ها روی آن‌ها هم اعمال خواهند شد.

این طرح ویژگی‌های خاص مستندسازی زیر را فراهم می‌کند. این ویژگی‌ها فقط در این طرح فعال هستند.

- پیوند ویرایش
- پیوند قبلی و بعدی
- ساختار
- [تبلیغات Carbon](./default-theme-carbon-ads)

## طرح Page {#page-layout}

گزینه `page` به عنوان "صفحه خالی" در نظر گرفته می‌شود. Markdown همچنان تجزیه و تحلیل می‌شود و تمامی [توسعه‌های Markdown](../guide/markdown) به عنوان طرح `doc` کار می‌کنند، اما هیچ استایل پیش‌فرضی به آن اعمال نمی‌شود.

طرح صفحه به شما این امکان را می‌دهد که همه چیز را به دلخواه خود شخصی‌سازی کنید بدون این که طرح ویت‌پرس بر روی مارک‌آپ تاثیر بگذارد. این کار بسیار مفید است زمانی که می‌خواهید صفحه سفارشی خود را ایجاد کنید.

توجه داشته باشید که حتی در این طرح، نوار کناری نیز نمایش داده می‌شود اگر صفحه دارای پیکربندی نوار کناری مطابق باشد.

## طرح Home  {#home-layout}

گزینه `home` صفحه "خانه" قالب‌بندی می‌کند. در این طرح، می‌توانید گزینه‌های اضافی مانند `hero` و `features` را برای دلخواه‌سازی محتوا تنظیم کنید. لطفاً [صفحه پیش‌فرض: صفحه خانه](./default-theme-home-page) را برای اطلاعات بیشتر مشاهده کنید.

## بدون طرح {#no-layout}

اگر نمی‌خواهید هیچ طرحی داشته باشید، می‌توانید با گذراندن `layout: false` از frontmatter، از این گزینه استفاده کنید. این گزینه مفید است اگر صفحهٔ نخستی کاملاً قابل تنظیم (بدون هیچ نوار کناری، نوار ناوبری یا پاورقی به صورت پیش‌فرض) را می‌خواهید.

## طرح سفارشی {#custom-layout}

همچنین می‌توانید از یک طرح سفارشی استفاده کنید:

```md
---
layout: foo
---
```

این دستور به دنبال یک کامپوننت به نام `foo` ثبت شده در محیط است. به عنوان مثال، می‌توانید کامپوننت خود را به صورت گلوبال در `.vitepress/theme/index.ts` ثبت کنید:

```ts
import DefaultTheme from 'vitepress/theme'
import Foo from './Foo.vue'

export default {
  extends: DefaultTheme,
  enhanceApp({ app }) {
    app.component('foo', Foo)
  }
}
```
