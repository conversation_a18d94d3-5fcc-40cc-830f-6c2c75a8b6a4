# Переход с VuePress

## Конфигурация

### Сайдбар

Боковая панель больше не заполняется автоматически из метаданных. Вы можете [самостоятельно прочитать вступление](https://github.com/vuejs/vitepress/issues/572#issuecomment-1170116225), чтобы научиться динамически заполнять боковую панель. [Дополнительные утилиты для этого](https://github.com/vuejs/vitepress/issues/96) могут быть предоставлены в будущем.

## Markdown

### Изображения

В отличие от VuePress, VitePress автоматически обрабатывает опцию [`base`](./asset-handling#base-url) вашего конфига, когда вы используете статическое изображение.

Таким образом, теперь вы можете выводить изображения без тега `img`.

```diff
- <img :src="$withBase('/foo.png')" alt="foo">
+ ![foo](/foo.png)
```

::: warning ПРЕДУПРЕЖДЕНИЕ
Для динамических изображений вам всё ещё нужно использовать `withBase`, как показано в [Руководстве по базовому URL](./asset-handling#base-url).
:::

Используйте регулярное выражение `<img.*withBase\('(.*)'\).*alt="([^"]*)".*>` для поиска всех изображений с синтаксисом `![](...)` и замены на `![$2]($1)`.

---

продолжение следует...
