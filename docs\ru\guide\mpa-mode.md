# Режим MPA <Badge type="warning" text="экспериментально" /> {#mpa-mode}

Режим MPA (Multi-Page Application — «Многостраничное приложение») можно включить через командную строку с помощью команды `vitepress build --mpa`, или через конфигурацию с помощью опции `mpa: true`.

В режиме MPA все страницы по умолчанию отображаются без включенного JavaScript. В результате производственный сайт, скорее всего, получит более высокую оценку эффективности первых посещений с помощью инструментов аудита.

Однако из-за отсутствия навигации SPA межстраничные ссылки будут приводить к полной перезагрузке страницы. После загрузки навигация в режиме MPA будет не такой мгновенной, как в режиме SPA.

Также обратите внимание, что «no-JS-by-default» («без JS по умолчанию») означает, что вы используете Vue исключительно как серверный язык шаблонов. Никаких обработчиков событий в браузере не будет, как и интерактивности. Чтобы загрузить JavaScript со стороны клиента, вам нужно использовать специальный тег `<script client>`:

```html
<script client>
  document.querySelector('h1').addEventListener('click', () => {
    console.log('JavaScript на стороне клиента!')
  })
</script>

# Привет
```

`<script client>` — это функция только для VitePress, а не для Vue. Она работает как в файлах `.md`, так и в файлах `.vue`, но только в режиме MPA. Клиентские скрипты во всех компонентах темы будут объединены вместе, в то время как клиентский скрипт для конкретной страницы будет разделён только для этой страницы.

Обратите внимание, что `<script client>` **не оценивается как код компонента Vue**: он обрабатывается как обычный модуль JavaScript. По этой причине режим MPA следует использовать только в том случае, если ваш сайт требует абсолютно минимальной интерактивности на стороне клиента.
