name: Test

on:
  push:
    branches: [main]
  pull_request:
    branches: [main]
  workflow_dispatch:

permissions: {}

concurrency:
  group: ${{ github.workflow }}-${{ github.event.number || github.sha }}
  cancel-in-progress: true

jobs:
  test:
    strategy:
      matrix:
        os: [ubuntu-latest]
        node_version: [20, 22, latest]
        include:
          - os: windows-latest
            node_version: 22

    runs-on: ${{ matrix.os }}

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Install pnpm
        uses: pnpm/action-setup@v3

      - name: Set node version to ${{ matrix.node_version }}
        uses: actions/setup-node@v4
        with:
          node-version: ${{ matrix.node_version }}
          cache: pnpm

      - name: Install deps
        run: pnpm install
        env:
          PLAYWRIGHT_SKIP_BROWSER_DOWNLOAD: 1

      - name: Install Playwright
        run: pnpm playwright install chromium

      - name: Check
        run: pnpm check
