---
outline: deep
---

# Подключение к CMS {#connecting-to-a-cms}

## Общий рабочий процесс {#general-workflow}

Подключение VitePress к CMS в значительной степени зависит от [динамических маршрутов](./routing#dynamic-routes). Прежде чем приступить к работе, убедитесь, что вы понимаете, как это работает.

Поскольку каждая CMS работает по-своему, здесь мы можем предоставить лишь общую схему работы, которую вам нужно будет адаптировать под свой конкретный сценарий.

1. Если ваша CMS требует аутентификации, создайте файл `.env` для хранения токенов API и загрузите его таким образом:

   ```js
   // posts/[id].paths.js
   import { loadEnv } from 'vitepress'

   const env = loadEnv('', process.cwd())
   ```

2. Получите необходимые данные из CMS и преобразуйте их в соответствующие пути:

   ```js
   export default {
     async paths() {
       // при необходимости используйте соответствующую клиентскую библиотеку CMS
       const data = await (
         await fetch('https://my-cms-api', {
           headers: {
             // токен, если необходимо
           }
         })
       ).json()

       return data.map((entry) => {
         return {
           params: { id: entry.id /* заголовок, автор, дата и т. д. */ },
           content: entry.content
         }
       })
     }
   }
   ```

3. Отрисуйте содержимое страницы:

   ```md
   # {{ $params.title }}

   - Автор: {{ $params.author }}, {{ $params.date }}

   <!-- @content -->
   ```

## Руководства по интеграции {#integration-guides}

Если вы написали руководство по интеграции VitePress с конкретной CMS, воспользуйтесь ссылкой «Редактировать эту страницу», чтобы добавить его сюда!
