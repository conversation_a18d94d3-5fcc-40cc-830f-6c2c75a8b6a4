---
layout: home

title: VitePress

hero:
  name: VitePress
  text: Vite & Vue Powered Static Site Generator
  actions:
    - theme: brand
      text: Examples
      link: /home

features:
  - title: Emoji
    details: Emoji on features section
    icon: ⚡️
  - title: SVG
    details: SVG on features section
    icon:
      src: /vitepress.svg
      alt: VitePress Logo
  - title: PNG
    details: PNG on features section
    icon:
      src: /vitepress.png
      width: 48
      height: 48
      alt: VitePress Logo
  - title: Dark/Light SVG
    details: Dark/Light SVG on features section
    icon:
      dark: /pwa_dark.svg
      light: /pwa_light.svg
      alt: Vite PWA Logo
---
