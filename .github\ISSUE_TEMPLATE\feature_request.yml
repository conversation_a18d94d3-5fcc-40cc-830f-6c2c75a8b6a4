name: "\U0001F680 New feature proposal"
description: Suggest an idea for this project
body:
  - type: markdown
    attributes:
      value: |
        Thanks for your interest in the project and taking the time to fill out this feature report!
  - type: textarea
    id: feature-description
    attributes:
      label: Is your feature request related to a problem? Please describe.
      description: "A clear and concise description of what the problem is. Ex. I'm always frustrated when [...]"
    validations:
      required: true
  - type: textarea
    id: suggested-solution
    attributes:
      label: Describe the solution you'd like
      description: A clear and concise description of what you want to happen.
    validations:
      required: true
  - type: textarea
    id: alternative
    attributes:
      label: Describe alternatives you've considered
      description: A clear and concise description of any alternative solutions or features you've considered.
  - type: textarea
    id: additional-context
    attributes:
      label: Additional context
      description: Add any other context or screenshots about the feature request here.
  - type: checkboxes
    id: checkboxes
    attributes:
      label: Validations
      description: Before submitting the issue, please make sure you do the following
      options:
        - label: Follow our [Code of Conduct](https://vuejs.org/about/coc.html)
          required: true
        - label: Read the [docs](https://vitepress.dev).
          required: true
        - label: Read the [Contributing Guidelines](https://github.com/vuejs/vitepress/blob/main/.github/contributing.md).
          required: true
        - label: Check that there isn't already an issue that asks for the same feature to avoid creating a duplicate.
          required: true
