{"name": "docs", "private": true, "type": "module", "scripts": {"dev": "vitepress dev", "build": "vitepress build", "preview": "vitepress preview", "lunaria:build": "<PERSON><PERSON> build", "lunaria:open": "open-cli .vitepress/dist/_translations/index.html"}, "devDependencies": {"@lunariajs/core": "^0.1.1", "markdown-it-mathjax3": "^4.3.2", "open-cli": "^8.0.0", "postcss-rtlcss": "^5.7.1", "vitepress": "workspace:*", "vitepress-plugin-group-icons": "^1.6.0", "vitepress-plugin-llms": "^1.5.1"}}