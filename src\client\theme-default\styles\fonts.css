/* webfont-marker-begin */
@import url('https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&display=swap');
/* webfont-marker-end */

@font-face {
  font-family: Inter;
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url('../fonts/inter-roman-cyrillic-ext.woff2') format('woff2');
  unicode-range:
    U+0460-052F, U+1C80-1C88, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}

@font-face {
  font-family: Inter;
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url('../fonts/inter-roman-cyrillic.woff2') format('woff2');
  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}

@font-face {
  font-family: Inter;
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url('../fonts/inter-roman-greek-ext.woff2') format('woff2');
  unicode-range: U+1F00-1FFF;
}

@font-face {
  font-family: Inter;
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url('../fonts/inter-roman-greek.woff2') format('woff2');
  unicode-range:
    U+0370-0377, U+037A-037F, U+0384-038A, U+038C, U+038E-03A1, U+03A3-03FF;
}

@font-face {
  font-family: Inter;
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url('../fonts/inter-roman-vietnamese.woff2') format('woff2');
  unicode-range:
    U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1,
    U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329,
    U+1EA0-1EF9, U+20AB;
}

@font-face {
  font-family: Inter;
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url('../fonts/inter-roman-latin-ext.woff2') format('woff2');
  unicode-range:
    U+0100-02AF, U+0304, U+0308, U+0329, U+1E00-1E9F, U+1EF2-1EFF, U+2020,
    U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}

@font-face {
  font-family: Inter;
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url('../fonts/inter-roman-latin.woff2') format('woff2');
  unicode-range:
    U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC,
    U+0304, U+0308, U+0329, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193,
    U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: Inter;
  font-style: italic;
  font-weight: 100 900;
  font-display: swap;
  src: url('../fonts/inter-italic-cyrillic-ext.woff2') format('woff2');
  unicode-range:
    U+0460-052F, U+1C80-1C88, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}

@font-face {
  font-family: Inter;
  font-style: italic;
  font-weight: 100 900;
  font-display: swap;
  src: url('../fonts/inter-italic-cyrillic.woff2') format('woff2');
  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}

@font-face {
  font-family: Inter;
  font-style: italic;
  font-weight: 100 900;
  font-display: swap;
  src: url('../fonts/inter-italic-greek-ext.woff2') format('woff2');
  unicode-range: U+1F00-1FFF;
}

@font-face {
  font-family: Inter;
  font-style: italic;
  font-weight: 100 900;
  font-display: swap;
  src: url('../fonts/inter-italic-greek.woff2') format('woff2');
  unicode-range:
    U+0370-0377, U+037A-037F, U+0384-038A, U+038C, U+038E-03A1, U+03A3-03FF;
}

@font-face {
  font-family: Inter;
  font-style: italic;
  font-weight: 100 900;
  font-display: swap;
  src: url('../fonts/inter-italic-vietnamese.woff2') format('woff2');
  unicode-range:
    U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1,
    U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329,
    U+1EA0-1EF9, U+20AB;
}

@font-face {
  font-family: Inter;
  font-style: italic;
  font-weight: 100 900;
  font-display: swap;
  src: url('../fonts/inter-italic-latin-ext.woff2') format('woff2');
  unicode-range:
    U+0100-02AF, U+0304, U+0308, U+0329, U+1E00-1E9F, U+1EF2-1EFF, U+2020,
    U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}

@font-face {
  font-family: Inter;
  font-style: italic;
  font-weight: 100 900;
  font-display: swap;
  src: url('../fonts/inter-italic-latin.woff2') format('woff2');
  unicode-range:
    U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC,
    U+0304, U+0308, U+0329, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193,
    U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: 'Punctuation SC';
  font-weight: 400;
  src:
    local('PingFang SC Regular'), local('Noto Sans CJK SC'),
    local('Microsoft YaHei');
  unicode-range:
    U+201C, U+201D, U+2018, U+2019, U+2E3A, U+2014, U+2013, U+2026, U+00B7,
    U+007E, U+002F;
}

@font-face {
  font-family: 'Punctuation SC';
  font-weight: 500;
  src:
    local('PingFang SC Medium'), local('Noto Sans CJK SC'),
    local('Microsoft YaHei');
  unicode-range:
    U+201C, U+201D, U+2018, U+2019, U+2E3A, U+2014, U+2013, U+2026, U+00B7,
    U+007E, U+002F;
}

@font-face {
  font-family: 'Punctuation SC';
  font-weight: 600;
  src:
    local('PingFang SC Semibold'), local('Noto Sans CJK SC Bold'),
    local('Microsoft YaHei Bold');
  unicode-range:
    U+201C, U+201D, U+2018, U+2019, U+2E3A, U+2014, U+2013, U+2026, U+00B7,
    U+007E, U+002F;
}

@font-face {
  font-family: 'Punctuation SC';
  font-weight: 700;
  src:
    local('PingFang SC Semibold'), local('Noto Sans CJK SC Bold'),
    local('Microsoft YaHei Bold');
  unicode-range:
    U+201C, U+201D, U+2018, U+2019, U+2E3A, U+2014, U+2013, U+2026, U+00B7,
    U+007E, U+002F;
}

/* Generate the subsetted fonts using: `pyftsubset <file>.woff2 --unicodes="<range>" --output-file="inter-<style>-<subset>.woff2" --flavor=woff2` */
