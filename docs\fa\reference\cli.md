# رابط خط فرمان {#command-line-interface}

## `vitepress dev` {#vitepress-dev}

شروع سرور توسعه ویت‌پرس با استفاده از دایرکتوری مشخص به عنوان ریشه. به طور پیش‌فرض از دایرکتوری فعلی استفاده می‌شود. دستور `dev` همچنین می‌تواند حذف شود زمانی که در دایرکتوری فعلی اجرا می‌شود.

### استفاده {#usage}

```sh
# شروع در دایرکتوری فعلی، بدون `dev`
vitepress

# شروع در زیردایرکتوری
vitepress dev [root]
```

### گزینه‌ها {#options}

| گزینه          | توضیحات                                                         |
| --------------- | ----------------------------------------------------------------- |
| `--open [path]` | باز کردن مرورگر در زمان راه‌اندازی (`boolean \| string`)       |
| `--port <port>` | تعیین پورت (`number`)                                           |
| `--base <path>` | مسیر پایه عمومی (پیش‌فرض: `/`) (`string`)                        |
| `--cors`        | فعال‌سازی CORS                                                   |
| `--strictPort`  | خروج در صورت استفاده از پورت مشخص شده (`boolean`)              |
| `--force`       | اجبار به نادیده گرفتن حافظه پنهان و بازسازی (`boolean`)       |

## `vitepress build` {#vitepress-build}

ساخت سایت ویت‌پرس برای تولید نهایی.

### استفاده {#usage-1}

```sh
vitepress build [root]
```

### گزینه‌ها {#options-1}

| گزینه                         | توضیحات                                                                                                          |
| ------------------------------ | ---------------------------------------------------------------------------------------------------------------- |
| `--mpa` (آزمایشی)             | ساخت در حالت [MPA](../guide/mpa-mode) بدون هیدراسیون سمت مشتری (`boolean`)                                      |
| `--base <path>`                | مسیر پایه عمومی (پیش‌فرض: `/`) (`string`)                                                                       |
| `--target <target>`            | هدف ترنسپایل (پیش‌فرض: `"modules"`) (`string`)                                                                 |
| `--outDir <dir>`               | دایرکتوری خروجی نسبت به **cwd** (پیش‌فرض: `<root>/.vitepress/dist`) (`string`)                                |
| `--assetsInlineLimit <number>` | آستانه تبدیل پایه ۶۴ استاتیک به بایت (پیش‌فرض: `4096`) (`number`)                                             |

## `vitepress preview` {#vitepress-preview}

پیش‌نمایش محلی برای ساخت تولیدی را نمایش دهید.

### استفاده {#usage-2}

```sh
vitepress preview [root]
```

### گزینه‌ها {#options-2}

| گزینه          | توضیحات                                |
| --------------- | ---------------------------------------- |
| `--base <path>` | مسیر پایه عمومی (پیش‌فرض: `/`) (`string`) |
| `--port <port>` | تعیین پورت (`number`)                   |

## `vitepress init` {#vitepress-init}

شروع [جادوگر راه‌اندازی](../guide/getting-started#setup-wizard) در دایرکتوری فعلی.

### استفاده {#usage-3} 

```sh
vitepress init
```
