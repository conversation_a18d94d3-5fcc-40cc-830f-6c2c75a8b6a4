# مدیریت منابع {#asset-handling}

## ارجاع به منابع ایستا {#referencing-static-assets}

تمام فایل‌های Markdown به کامپوننت‌های Vue تبدیل و توسط [Vite](https://vitejs.dev/guide/assets.html) پردازش می‌شوند. شما می‌توانید، **و باید**، هر نوع دارایی را با استفاده از URL‌های نسبی مرجع قرار دهید:

```md
![تصویر](./image.png)
```

شما می‌توانید منابع ایستا را در فایل‌های Markdown خود، کامپوننت‌های `*.vue` در قالب، استایل‌ها و فایل‌های `.css` ساده، با استفاده از مسیرهای عمومی مطلق (براساس ریشه پروژه) یا مسیرهای نسبی (براساس سیستم فایل شما) ارجاع دهید. روش دوم مشابه رفتاری است که در صورت استفاده از Vite، Vue CLI یا `file-loader` webpack با آن آشنا هستید.

انواع شایع تصویر، رسانه و فایل فونت به طور خودکار شناسایی و به عنوان منابع درج می‌شوند.

::: tip فایل‌های لینک شده به عنوان دارایی محسوب نمی‌شوند
PDFها یا سند‌های دیگر که از طریق پیوندها در فایل‌های Markdown ارجاع داده شده‌اند به طور خودکار به عنوان دارایی در نظر گرفته نمی‌شوند. برای دسترسی به فایل‌های لینک شده، باید آن‌ها را به صورت دستی در دایرکتوری [`public`](#the-public-directory) پروژه قرار دهید.
:::

تمام منابع ارجاع داده شده، شامل آن‌هایی که از مسیرهای مطلق استفاده می‌کنند، در مرحله تولید به دایرکتوری خروجی با نام فایلی بر اساس یک هش کپی خواهند شد. دارایی‌هایی که هرگز ارجاع نداده شوند، کپی نخواهند شد. منابع تصویر کوچک‌تر از 4 کیلوبایت به صورت base64 درون خطی می‌شوند - این می‌تواند از طریق گزینه پیکربندی [`vite`](../reference/site-config#vite) تنظیم شود.

تمام ارجاع‌های مسیر **ایستا**، شامل مسیرهای مطلق، باید بر اساس ساختار دایرکتوری کاری شما تعیین شوند.

## دایرکتوری عمومی {#the-public-directory}

گاهی اوقات ممکن است نیاز داشته باشید منابع ایستا را فراهم کنید که به صورت مستقیم در هیچ‌یک از Markdown یا کامپوننت‌های قالب شما ارجاع نشده‌اند، یا ممکن است بخواهید برخی فایل‌ها را با نام اصلی خود سرویس دهید. به عنوان مثال، فایل‌هایی مانند `robots.txt`، آیکون‌های fav، و آیکون‌های PWA.

شما می‌توانید این فایل‌ها را در دایرکتوری `public` تحت [دایرکتوری منبع](./routing#source-directory) قرار دهید. به عنوان مثال، اگر ریشه پروژه شما `./docs` است و از محل پیش‌فرض دایرکتوری منبع استفاده می‌کنید، آنگاه دایرکتوری عمومی شما `./docs/public` خواهد بود.

منابع قرار داده شده در `public` به صورت اصلی در ریشه دایرکتوری خروجی کپی خواهند شد.

توجه داشته باشید که باید به فایل‌های قرار داده شده در `public` با استفاده از مسیر مطلق ریشه ارجاع دهید - به عنوان مثال، `public/icon.png` همیشه باید به عنوان `/icon.png` در کد منبع ارجاع داده شود.

## URL پایه {#base-url}

اگر وب‌سایت شما به URL غیر ریشه استقرار می‌یابد، باید گزینه `base` را در `.vitepress/config.js` تنظیم کنید. به عنوان مثال، اگر قصد دارید وب‌سایت خود را به `https://foo.github.io/bar/` استقرار دهید، آنگاه `base` باید به `'/bar/'` تنظیم شود (همیشه باید با یک خط شروع و پایان یابد).

تمام مسیرهای دارایی ایستا شما به صورت خودکار پردازش می‌شوند تا با ارزش‌های `base` مختلف تطبیق یابند. به عنوان مثال، اگر به یک ارجاع مطلق به یک دارایی زیر `public` در Markdown خود اشاره کرده‌اید:

```md
![تصویر](/image-inside-public.png)
```

در این حالت، شما **نیازی ندارید** که آن را به روز کنید وقتی که مقدار پیکربندی `base` را تغییر می‌دهید.

اما، اگر شما در حال نویسندگی یک کامپوننت قالب هستید که به صورت پویا به منابع لینک می‌دهد، به عنوان مثال یک تصویر که `src` آن براساس مقدار پیکربندی قالب است:

```vue
<img :src="theme.logoPath" />
```

در این حالت، توصیه می‌شود که مسیر را با استفاده از کمکی [`withBase`](../reference/runtime-api#withbase) ارائه شده توسط ویت‌پرس بپوشانید:

```vue
<script setup>
import { withBase, useData } from 'vitepress'

const { theme } = useData()
</script>

<template>
  <img :src="withBase(theme.logoPath)" />
</template>
```
