# Первые шаги {#getting-started}

## Попробуйте онлайн {#try-it-online}

Вы можете попробовать VitePress прямо в браузере на [StackBlitz](https://vitepress.new).

## Установка {#installation}

### Требования {#prerequisites}

- [Node.js](https://nodejs.org/) версии 18 или выше.
- Терминал для доступа к VitePress через интерфейс командной строки (CLI).
- Текстовый редактор с поддержкой синтаксиса [Markdown](https://ru.wikipedia.org/wiki/Markdown).
  - Рекомендуется использовать [VSCode](https://code.visualstudio.com/), а также [официальное расширение Vue](https://marketplace.visualstudio.com/items?itemName=Vue.volar).

VitePress можно использовать самостоятельно или установить в существующий проект. В обоих случаях вы можете установить его с помощью:

::: code-group

```sh [npm]
$ npm add -D vitepress
```

```sh [pnpm]
$ pnpm add -D vitepress
```

```sh [yarn]
$ yarn add -D vitepress
```

```sh [yarn (pnp)]
$ yarn add -D vitepress vue
```

```sh [bun]
$ bun add -D vitepress
```

:::

::: details Получаете предупреждения об отсутствующих зависимостях?
Если вы используете PNPM, вы заметите предупреждение об отсутствующем пакете `@docsearch/js`. Это не мешает работе VitePress. Если вы хотите подавить это предупреждение, добавьте следующее в ваш `package.json`:

```json
"pnpm": {
  "peerDependencyRules": {
    "ignoreMissing": [
      "@algolia/client-search",
      "search-insights"
    ]
  }
}
```

:::

::: tip ПРИМЕЧАНИЕ

VitePress — это пакет, предназначенный только для ESM. Не используйте `require()` для импорта, и убедитесь, что ближайший `package.json` содержит `"type": "module"`, или измените расширение соответствующих файлов, например, `.vitepress/config.js` на `.mjs`/`.mts`. Более подробную информацию см. в [Руководстве по устранению неполадок Vite](https://vitejs.dev/guide/troubleshooting.html#this-package-is-esm-only). Кроме того, внутри асинхронных контекстов CJS можно использовать `await import('vitepress')` вместо этого.

:::

### Мастер настройки {#setup-wizard}

VitePress поставляется с мастером настройки командной строки, который поможет вам создать базовый проект. После установки запустите мастер, выполнив команду:

::: code-group

```sh [npm]
$ npx vitepress init
```

```sh [pnpm]
$ pnpm vitepress init
```

```sh [yarn]
$ yarn vitepress init
```

```sh [bun]
$ bun vitepress init
```

:::

Вас встретят несколькими простыми вопросами:

<<< @/snippets/init.ansi

::: tip Vue как зависимость
Если вы собираетесь выполнять кастомизацию с использованием компонентов или API Vue, вам также следует явно установить `vue` в качестве зависимости.
:::

## Структура файлов {#file-structure}

Если вы создаете отдельный сайт VitePress, вы можете разместить его в текущей директории (`./`). Однако если вы устанавливаете VitePress в существующий проект вместе с другим исходным кодом, рекомендуется поместить сайт во вложенную директорию (например, `./docs`), чтобы он был отделён от остальной части проекта.

Если предположить, что вы решили разместить проект VitePress в `./docs`, то сгенерированная структура файлов должна выглядеть следующим образом:

```
.
├─ docs
│  ├─ .vitepress
│  │  └─ config.js
│  ├─ api-examples.md
│  ├─ markdown-examples.md
│  └─ index.md
└─ package.json
```

Директория `docs` считается **корнем проекта** сайта VitePress. Директория `.vitepress` — это зарезервированное место для конфигурационного файла VitePress, кэша dev-сервера, результатов сборки и дополнительного кода настройки темы.

::: tip СОВЕТ
По умолчанию VitePress хранит кэш своего dev-сервера в файле `.vitepress/cache`, а выходные данные сборки — в файле `.vitepress/dist`. Если вы используете Git, вам следует добавить их в файл `.gitignore`. Эти места также могут быть [сконфигурированы](../reference/site-config#outdir).
:::

### Конфигурационный файл {#the-config-file}

Файл конфигурации (`.vitepress/config.js`) позволяет настраивать различные аспекты сайта VitePress, самыми основными из которых являются название и описание сайта:

```js [.vitepress/config.js]
export default {
  // параметры сайта
  title: 'Заголовок сайта',
  description: 'Описание сайта.',

  themeConfig: {
    // параметры темы
  }
}
```

Вы также можете настроить поведение темы с помощью опции `themeConfig`. Загляните в главу [Конфигурация сайта](../reference/site-config) для получения подробной информации обо всех настраиваемых параметрах.

### Исходные файлы {#source-files}

Файлы Markdown за пределами директории `.vitepress` считаются **исходными файлами**.

VitePress использует **маршрутизацию на основе файлов**: Каждый файл `.md` компилируется в соответствующий файл `.html` с тем же путём. Например, `index.md` будет скомпилирован в `index.html`, и его можно будет посетить по корневому пути `/` результирующего сайта VitePress.

VitePress также предоставляет возможность генерировать чистые URL-адреса, переписывать пути и динамически генерировать страницы. Всё это будет рассмотрено в [Руководстве по маршрутизации](./routing).

## Скрипты запуска {#up-and-running}

Мастер настройки также должен был внедрить следующие скрипты npm в ваш `package.json`, если вы разрешили ему это сделать в процессе установки:

```json [package.json]
{
  ...
  "scripts": {
    "docs:dev": "vitepress dev docs",
    "docs:build": "vitepress build docs",
    "docs:preview": "vitepress preview docs"
  },
  ...
}
```

Скрипт `docs:dev` запустит локальный dev-сервер с мгновенными горячими обновлениями. Выполните следующую команду:

::: code-group

```sh [npm]
$ npm run docs:dev
```

```sh [pnpm]
$ pnpm run docs:dev
```

```sh [yarn]
$ yarn docs:dev
```

```sh [bun]
$ bun run docs:dev
```

:::

Вместо npm-скриптов вы также можете вызывать VitePress напрямую с помощью:

::: code-group

```sh [npm]
$ npx vitepress dev docs
```

```sh [pnpm]
$ pnpm vitepress dev docs
```

```sh [yarn]
$ yarn vitepress dev docs
```

```sh [bun]
$ bun vitepress dev docs
```

:::

Более подробная информация об использовании командной строки описана в главе [Командная строка](../reference/cli).

Dev-сервер должен быть запущен по адресу `http://localhost:5173`. Перейдите по URL-адресу в браузере, чтобы увидеть новый сайт в действии!

## Что дальше? {#what-s-next}

- Чтобы лучше понять, как Markdown-файлы сопоставляются с генерируемым HTML, перейдите к [Руководству по маршрутизации](./routing).

- Чтобы узнать больше о том, что вы можете делать на странице, например, писать содержимое в формате Markdown или использовать компоненты Vue, обратитесь к разделу «Написание». Начать стоит с изучения главы [Расширения Markdown](./markdown).

- Чтобы изучить возможности, предоставляемые темой документации по умолчанию, ознакомьтесь с главой [Настройка темы по умолчанию](../reference/default-theme-config).

- Если вы хотите ещё больше изменить внешний вид своего сайта, изучите главы [Расширение темы по умолчанию](./extending-default-theme) или [Пользовательская тема](./custom-theme).

- Как только ваш сайт документации обретёт форму, обязательно прочитайте [Руководство по развёртыванию](./deploy).
