# Последнее обновление {#last-updated}

Время последнего обновления содержимого будет отображаться в правом нижнем углу страницы. Чтобы включить его, добавьте опцию `lastUpdated` в свой конфиг.

::: info ПРИМЕЧАНИЕ
VitePress отображает время «последнего обновления» на основе временной метки последнего Git-коммита для каждого файла. Для работы этой функции Markdown-файл должен быть закоммичен в Git.

Внутри VitePress выполняет команду `git log -1 --pretty="%ai"` для каждого файла, чтобы получить его временную метку. Если все страницы показывают одинаковое время обновления, вероятно, это связано с поверхностным клонированием (часто встречается в CI-средах), которое ограничивает историю Git.

Чтобы исправить это в **GitHub Actions**, добавьте следующее в ваш workflow-файл:

```yaml{4}
- name: Checkout
  uses: actions/checkout@v4
  with:
    fetch-depth: 0
```

Другие CI/CD-платформы имеют аналогичные настройки.

Если такие опции недоступны, вы можете добавить принудительный fetch перед командой `docs:build` в вашем `package.json`:

```json
"docs:build": "git fetch --unshallow && vitepress build docs"
```
:::

## Настройка в файле конфигурации {#site-level-config}

```js
export default {
  lastUpdated: true
}
```

## Настройка в метаданных {#frontmatter-config}

Эту информацию можно отключить на конкретной странице с помощью опции `lastUpdated` в метаданных:

```yaml
---
lastUpdated: false
---
```

Также смотрите [Тема по умолчанию: `lastUpdated`](./default-theme-config#lastupdated) для получения более подробной информации. Любое истинное значение на уровне темы также включит функцию, если только она не будет явно отключена на уровне сайта или страницы.
