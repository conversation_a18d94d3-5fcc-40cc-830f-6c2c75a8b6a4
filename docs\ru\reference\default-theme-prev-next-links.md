# Предыдущая и следующая страницы {#prev-next-links}

Вы можете настроить текст и ссылку для предыдущей и следующей страниц (отображаются в нижней части страницы). Это полезно, если вы хотите, чтобы текст отличался от того, что находится в сайдбаре. Кроме того, вы можете счесть полезным отключить футер или ссылку на страницу, которая не включена в сайдбар.

## prev {#prev}

- Тип: `string | false | { text?: string; link?: string }`

- Подробности:

  Указывает текст/ссылку, который должен отображаться при переходе на предыдущую страницу. Если вы не зададите это в метаданных, текст/ссылка будет определяться из конфигурации сайдбара.

- Примеры:

  - Для настройки только текста:

    ```yaml
    ---
    prev: 'Начать | Markdown'
    ---
    ```

  - Для настройки текста и ссылки:

    ```yaml
    ---
    prev:
      text: 'Markdown'
      link: '/guide/markdown'
    ---
    ```

  - Для скрытия предыдущей страницы:

    ```yaml
    ---
    prev: false
    ---
    ```

## next {#next}

Аналогично параметру `prev`, но для следующей страницы.
