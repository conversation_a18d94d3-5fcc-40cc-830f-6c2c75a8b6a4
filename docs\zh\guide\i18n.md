# 国际化 {#internationalization}

要使用内置的 i18n (国际化) 功能，需要创建类似于下面的目录结构：

```
docs/
├─ es/
│  ├─ foo.md
├─ fr/
│  ├─ foo.md
├─ foo.md
```

然后在 `docs/.vitepress/config.ts` 中：

```ts [docs/.vitepress/config.ts]
import { defineConfig } from 'vitepress'

export default defineConfig({
  // 共享属性和其他顶层内容...

  locales: {
    root: {
      label: 'English',
      lang: 'en'
    },
    fr: {
      label: 'French',
      lang: 'fr', // 可选，将作为 `lang` 属性添加到 `html` 标签中
      link: '/fr/guide' // 默认 /fr/ -- 显示在导航栏翻译菜单上，可以是外部的

      // 其余 locale 特定属性...
    }
  }
})
```

下面的属性能够被每个 locale 覆盖 (包括 root)：

```ts
interface LocaleSpecificConfig<ThemeConfig = any> {
  lang?: string
  dir?: string
  title?: string
  titleTemplate?: string | boolean
  description?: string
  head?: HeadConfig[] // 将与现有的头部条目合并，重复的元标签将自动删除
  themeConfig?: ThemeConfig // 会进行浅层合并，常见内容可放在顶层的 themeConfig 属性中
}
```

有关自定义默认主题的文本占位符的信息，请参考 [`DefaultTheme.Config`](https://github.com/vuejs/vitepress/blob/main/types/default-theme.d.ts) 接口。不要在 locale 级别覆盖 `themeConfig.algolia` 或 `themeConfig.carbonAds`。想获取多语言搜索的信息，请参考 [Algolia 文档](../reference/default-theme-search#i18n)。

**提示**：配置文件也可以是 `docs/.vitepress/config/index.ts`。通过为每个语言环境创建一个配置文件，然后从 `index.ts` 合并并导出它们，可以更好的组织文件。

## 为本地化设置子目录 {#separate-directory-for-each-locale}

下面是一个组织良好的结构：

```
docs/
├─ en/
│  ├─ foo.md
├─ es/
│  ├─ foo.md
├─ fr/
   ├─ foo.md
```

但是，VitePress 默认不会将 `/` 重定向到 `/en/`。需要配置服务器来实现这一点。例如，在使用 Netlify 时，可以添加一个 `docs/public/_redirects` 文件，如下所示：

```
/*  /es/:splat  302  Language=es
/*  /fr/:splat  302  Language=fr
/*  /en/:splat  302
```

**提示：** 如果使用上述的方法，可以使用`nf_lang` cookie 来保存用户的语言选择。例如，可以在主题中添加以下代码：

```ts [docs/.vitepress/theme/index.ts]
import DefaultTheme from 'vitepress/theme'
import Layout from './Layout.vue'

export default {
  extends: DefaultTheme,
  Layout
}
```

```vue [docs/.vitepress/theme/Layout.vue]
<script setup lang="ts">
import DefaultTheme from 'vitepress/theme'
import { useData, inBrowser } from 'vitepress'
import { watchEffect } from 'vue'

const { lang } = useData()
watchEffect(() => {
  if (inBrowser) {
    document.cookie = `nf_lang=${lang.value}; expires=Mon, 1 Jan 2030 00:00:00 UTC; path=/`
  }
})
</script>

<template>
  <DefaultTheme.Layout />
</template>
```

## RTL 支持 (实验性功能) {#rtl-support-experimental}

支持 RTL 需要在配置中指定 `dir: 'rtl'` 并且使用一些 RTLCSS PostCSS 插件，例如 <https://github.com/MohammadYounes/rtlcss>, <https://github.com/vkalinichev/postcss-rtl> 或者 <https://github.com/elchininet/postcss-rtlcss>。需要配置 PostCSS 插件，使用 `:where([dir="ltr"])` 和 `:where([dir="rtl"])` 作为前缀，以防止 CSS 特异性问题。
