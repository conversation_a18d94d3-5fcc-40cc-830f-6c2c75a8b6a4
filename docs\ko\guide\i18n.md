# i18n {#internationalization}

내장된 i18n 기능을 사용하려면 다음과 같은 디렉토리 구조를 만들어야 합니다:

```
docs/
├─ es/
│  ├─ foo.md
├─ fr/
│  ├─ foo.md
├─ foo.md
```

그런 다음 `docs/.vitepress/config.ts`에서:

```ts [docs/.vitepress/config.ts]
import { defineConfig } from 'vitepress'

export default defineConfig({
  // 공통 프로퍼티 및 기타 최상위 항목...

  locales: {
    root: {
      label: '영어',
      lang: 'en'
    },
    fr: {
      label: '프랑스어',
      lang: 'fr', // 선택 사항, `html` 태그에 `lang` 어트리뷰트로 추가됩니다
      link: '/fr/guide' // 기본적으로 /fr/ -- navbar 번역 메뉴에 표시됩니다 (외부 링크 가능)

      // 다른 로케일 특유의 프로퍼티...
    }
  }
})
```

각 로케일(루트 포함)에 대해 다음 프로퍼티를 재정의할 수 있습니다:

```ts
interface LocaleSpecificConfig<ThemeConfig = any> {
  lang?: string
  dir?: string
  title?: string
  titleTemplate?: string | boolean
  description?: string
  head?: HeadConfig[] // 기존 헤드 항목과 병합되며, 중복된 메타 태그는 자동으로 제거됩니다
  themeConfig?: ThemeConfig // 얕게 병합됩니다, 공통 항목은 최상위 themeConfig 항목에 넣을 수 있습니다
}
```

기본 테마의 플레이스홀더 텍스트를 커스텀하는 방법에 대한 자세한 내용은 [`DefaultTheme.Config`](https://github.com/vuejs/vitepress/blob/main/types/default-theme.d.ts) 인터페이스를 참고하십시오. 로케일 수준에서 `themeConfig.algolia` 또는 `themeConfig.carbonAds`를 재정의하지 마십시오. 다국어 검색을 사용하려면 [Algolia docs](../reference/default-theme-search#i18n)를 참조하십시오.

**전문가 팁:** 구성 파일은 `docs/.vitepress/config/index.ts`로 저장할 수도 있습니다. 로케일마다 구성 파일을 생성한 후 `index.ts`에서 병합하고 내보내어 내용을 구성하는 데 도움이 될 수 있습니다.

## 각 로케일별 디렉토리 {#separate-directory-for-each-locale}

다음은 완벽한 구조입니다:

```
docs/
├─ en/
│  ├─ foo.md
├─ es/
│  ├─ foo.md
├─ fr/
   ├─ foo.md
```

그러나 기본적으로 VitePress는 `/`를 `/en/`로 리다이렉션하지 않습니다. 이를 위해 서버를 구성해야 합니다. 예를 들어, Netlify에서는 다음과 같이 `docs/public/_redirects` 파일을 추가할 수 있습니다:

```
/*  /es/:splat  302  Language=es
/*  /fr/:splat  302  Language=fr
/*  /en/:splat  302
```

**전문가 팁:** 위의 접근 방식을 사용하는 경우 `nf_lang` 쿠키를 사용하여 유저의 언어 선택을 유지할 수 있습니다:

```ts [docs/.vitepress/theme/index.ts]
import DefaultTheme from 'vitepress/theme'
import Layout from './Layout.vue'

export default {
  extends: DefaultTheme,
  Layout
}
```

```vue [docs/.vitepress/theme/Layout.vue]
<script setup lang="ts">
import DefaultTheme from 'vitepress/theme'
import { useData, inBrowser } from 'vitepress'
import { watchEffect } from 'vue'

const { lang } = useData()
watchEffect(() => {
  if (inBrowser) {
    document.cookie = `nf_lang=${lang.value}; expires=Mon, 1 Jan 2030 00:00:00 UTC; path=/`
  }
})
</script>

<template>
  <DefaultTheme.Layout />
</template>
```

## RTL 지원 (실험적) {#rtl-support-experimental}

RTL 지원을 위해, 구성 파일에 `dir: 'rtl'`을 지정하고 <https://github.com/MohammadYounes/rtlcss>, <https://github.com/vkalinichev/postcss-rtl> 또는 <https://github.com/elchininet/postcss-rtlcss>와 같은 RTLCSS PostCSS 플러그인을 사용하세요. PostCSS 플러그인을 구성하여 CSS 명시성 문제를 방지하기 위해 `:where([dir="ltr"])` 및 `:where([dir="rtl"])`을 접두사로 사용해야 합니다.
