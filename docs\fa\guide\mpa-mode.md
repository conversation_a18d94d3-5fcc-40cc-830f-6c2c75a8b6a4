# حالت MPA <Badge type="warning" text="آزمایشی" /> {#mpa-mode}

حالت MPA (برنامه چند صفحه) می‌تواند از طریق خط فرمان با `vitepress build --mpa` فعال شود، یا از طریق تنظیمات با گزینه `mpa: true`.

در حالت MPA، همه صفحات به طور پیش‌فرض بدون هیچ جاوااسکریپتی رندر می‌شوند. به همین دلیل، سایت تولیدی احتمالاً امتیاز بهتری از ابزارهای آزمایشی در اولین بازدید دریافت خواهد کرد.

با این حال، به دلیل عدم وجود مسیریابی SPA، لینک‌های متقاطع به بازنشانی کامل صفحه منتهی می‌شوند. ناوبری پس از بارگیری در حالت MPA حساسیت به همان اندازه با حالت SPA نخواهد داشت.

همچنین توجه داشته باشید که عدم وجود JS به طور پیش‌فرض به این معنی است که شما اساساً Vue را به عنوان یک زبان قالب‌بندی سمت سرور استفاده می‌کنید. هیچ کنترل کننده رویدادی در مرورگر اضافه نمی‌شود، بنابراین هیچ تعاملی وجود نخواهد داشت. برای بارگیری JS سمت کلاینت، شما باید از تگ خاص `<script client>` استفاده کنید:

```html
<script client>
document.querySelector('h1').addEventListener('click', () => {
  console.log('JavaScript سمت کلاینت!')
})
</script>

# سلام
```

`<script client>` یک ویژگی تنها برای ویت‌پرس است، نه یک ویژگی Vue. این در هر دو فایل `.md` و `.vue` کار می‌کند، اما فقط در حالت MPA. اسکریپت‌های کلاینت در تمام اجزای تم با هم بسته می‌شوند، در حالی که اسکریپت کلاینت برای یک صفحه خاص، فقط برای آن صفحه تقسیم می‌شود.

توجه داشته باشید که `<script client>` به عنوان **کد مؤلفه مؤلفه Vue** ارزیابی نمی‌شود: به عنوان یک ماژول جاوااسکریپت معمولی پردازش می‌شود. به همین دلیل، حالت MPA فقط باید در صورتی استفاده شود که سایت شما به تعامل کمینه‌ای از جانب کلاینت نیاز دارد.
