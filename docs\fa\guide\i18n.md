# بین‌المللی‌سازی {#internationalization}

برای استفاده از ویژگی‌های داخلی بین‌المللی‌سازی، نیاز است که یک ساختار دایرکتوری به شکل زیر ایجاد کنید:

```
docs/
├─ es/
│  ├─ foo.md
├─ fr/
│  ├─ foo.md
├─ foo.md
```

سپس در `docs/.vitepress/config.ts` به شکل زیر عمل کنید:

```ts [docs/.vitepress/config.ts]
import { defineConfig } from 'vitepress'

export default defineConfig({
  // ویژگی‌های مشترک و دیگر موارد در سطح بالا...

  locales: {
    root: {
      label: 'انگلیسی',
      lang: 'en'
    },
    fr: {
      label: 'فرانسوی',
      lang: 'fr', // اختیاری، به عنوان `lang` در `html` tag اضافه خواهد شد
      link: '/fr/guide' // پیش‌فرض /fr/ -- در منوی ترجمه‌ها نمایش داده می‌شود، می‌تواند خارجی باشد

      // سایر ویژگی‌های مختص به هر زبان...
    }
  }
})
```

می‌توانید خصوصیات زیر را برای هر زبان (شامل ریشه) نیز تغییر دهید:

```ts
interface LocaleSpecificConfig<ThemeConfig = any> {
  lang?: string
  dir?: string
  title?: string
  titleTemplate?: string | boolean
  description?: string
  head?: HeadConfig[] // با ورودی‌های head موجود ادغام خواهد شد، meta tags تکراری به طور خودکار حذف می‌شوند
  themeConfig?: ThemeConfig // ادغام سطح بالا، می‌توان اطلاعات مشترک را در ورودی themeConfig اضافه کرد
}
```

برای جزئیات درباره تنظیمات پیش‌فرض، به رابط `DefaultTheme.Config` در [اینجا](https://github.com/vuejs/vitepress/blob/main/types/default-theme.d.ts) مراجعه کنید. لطفاً خصوصیات `themeConfig.algolia` یا `themeConfig.carbonAds` را در سطح زبان تغییر ندهید. برای استفاده از جستجوی چندزبانه، به [مستندات Algolia](../reference/default-theme-search#i18n) مراجعه کنید.

**نکته حرفه‌ای:** فایل پیکربندی را می‌توانید در `docs/.vitepress/config/index.ts` نیز ذخیره کنید. این کار به شما کمک می‌کند که با ایجاد یک فایل پیکربندی برای هر زبان و سپس ادغام و صدور آنها از `index.ts`، موارد را سازماندهی کنید.

## دایرکتوری جداگانه برای هر زبان {#separate-directory-for-each-locale}

ساختار زیر به طور کاملاً صحیح است:

```
docs/
├─ en/
│  ├─ foo.md
├─ es/
│  ├─ foo.md
├─ fr/
   ├─ foo.md
```

با این حال، ویت‌پرس به طور پیش‌فرض به `/` به `/en/` هدایت نمی‌کند. برای این کار باید سرور خود را پیکربندی کنید. به عنوان مثال، در Netlify، می‌توانید فایل `docs/public/_redirects` را به این شکل اضافه کنید:

```
/*  /es/:splat  302  Language=es
/*  /fr/:splat  302  Language=fr
/*  /en/:splat  302
```

**نکته حرفه‌ای:** در صورت استفاده از روش فوق، می‌توانید از کوکی `nf_lang` برای ثبت انتخاب زبان کاربر استفاده کنید:

```ts [docs/.vitepress/theme/index.ts]
import DefaultTheme from 'vitepress/theme'
import Layout from './Layout.vue'

export default {
  extends: DefaultTheme,
  Layout
}
```

```vue [docs/.vitepress/theme/Layout.vue]
<script setup lang="ts">
import DefaultTheme from 'vitepress/theme'
import { useData } from 'vitepress'
import { watchEffect } from 'vue'

const { lang } = useData()
watchEffect(() => {
  if (inBrowser) {
    document.cookie = `nf_lang=${lang.value}; expires=Mon, 1 Jan 2030 00:00:00 UTC; path=/`
  }
})
</script>

<template>
  <DefaultTheme.Layout />
</template>
```

## پشتیبانی از RTL (آزمایشی) {#rtl-support-experimental}

برای پشتیبانی از RTL، `dir: 'rtl'` را در پیکربندی مشخص کنید و از پلاگین‌های PostCSS RTLCSS مانند <https://github.com/MohammadYounes/rtlcss>، <https://github.com/vkalinichev/postcss-rtl> یا <https://github.com/elchininet/postcss-rtlcss> استفاده کنید. باید پلاگین PostCSS خود را به کارگیری `:where([dir="ltr"])` و `:where([dir="rtl"])` به عنوان پیشوندها جلوگیری از مشکلات اولویت CSS استفاده کنید.
