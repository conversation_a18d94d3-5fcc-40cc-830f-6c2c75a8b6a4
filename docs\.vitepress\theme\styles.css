:root:where(:lang(fa)) {
  --vp-font-family-base:
    'Vazirmatn', 'Inter', ui-sans-serif, system-ui, sans-serif,
    'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
}

:root {
  --vp-home-hero-name-color: transparent;
  --vp-home-hero-name-background: -webkit-linear-gradient(
    120deg,
    #bd34fe 30%,
    #41d1ff
  );
  --vp-home-hero-image-background-image: linear-gradient(
    -45deg,
    #bd34fe 50%,
    #47caff 50%
  );
  --vp-home-hero-image-filter: blur(44px);
}

@media (min-width: 640px) {
  :root {
    --vp-home-hero-image-filter: blur(56px);
  }
}

@media (min-width: 960px) {
  :root {
    --vp-home-hero-image-filter: blur(68px);
  }
}

.VPHero .VPImage {
  filter: drop-shadow(-2px 4px 6px rgba(0, 0, 0, 0.2));
  padding: 18px;
}

/* used in reference/default-theme-search */
img[src='/search.png'] {
  width: 100%;
  aspect-ratio: 1 / 1;
}
