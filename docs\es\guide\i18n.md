# Internacionalización {#internationalization}

Para usar recursos de i18n integrados, es necesario crear una estructura de directorios de la siguiente forma:

```
docs/
├─ es/
│  ├─ foo.md
├─ fr/
│  ├─ foo.md
├─ foo.md
```

En seguida, en el archivo `docs/.vitepress/config.ts`:

```ts [docs/.vitepress/config.ts]
import { defineConfig } from 'vitepress'

export default defineConfig({
  // propiedades compartidas e otras cosas de nivel superior...

  locales: {
    root: {
      label: 'English',
      lang: 'en'
    },
    fr: {
      label: 'French',
      lang: 'fr', // opcional, será adicionado como atributo `lang` en el tag `html`
      link: '/fr/guide' // por defecto /fr/ -- aparece en el menu de traducciones de la barra de navegación, puede ser externo

      // otras propiedades específicas de cada idioma...
    }
  }
})
```

Las siguientes propiedades pueden ser substituidas para cada idioma (incluyendo la raiz):

```ts
interface LocaleSpecificConfig<ThemeConfig = any> {
  lang?: string
  dir?: string
  title?: string
  titleTemplate?: string | boolean
  description?: string
  head?: HeadConfig[] // será mezclado con las entradas head existentes, las metatags duplicadas son removidas automáticamente
  themeConfig?: ThemeConfig // será mezclado superficialmente, cosas comunes pueden ser colocadas en la entrada superios  de themeConfig
}
```

Consulte la interfaz [`DefaultTheme.Config`](https://github.com/vuejs/vitepress/blob/main/types/default-theme.d.ts) para obtener detaller sobre la personalización de los textos marcadores del tema por defecto. No substituya `themeConfig.algolia` o `themeConfig.carbonAds` en el nivel de idioma. Consulte la [documentação Algolia](../reference/default-theme-search#i18n) para usar la busqueda multilenguaje.

**Consejo profesional:** El archivo de configuración puede ser almacenado en `docs/.vitepress/config/index.ts` también. Esto puede ayudar a organizar las cosas creando un archivo de configuración por idioma y entonces mezclarlos y exportarlos a partir de `index.ts`.

## Directorio separado para cada localización {#separate-directory-for-each-locale}

La siguiente estructura es totalmente válida:

```
docs/
├─ en/
│  ├─ foo.md
├─ es/
│  ├─ foo.md
├─ fr/
   ├─ foo.md
```

Sin embargo, VitePress no redireccionará `/` para `/en/` por defecto. Necesitará configurar su servidor para esto. Por ejemplo, en Netlify, puede adicionar un archivo `docs/public/_redirects` asi:

```
/*  /es/:splat  302  Language=es
/*  /fr/:splat  302  Language=fr
/*  /en/:splat  302
```

**Consejo profesional:** Si está usando la forma descrita arriba, puede usar el cookie `nf_lang` para persistir la selección de idioma del usuario:

```ts [docs/.vitepress/theme/index.ts]
import DefaultTheme from 'vitepress/theme'
import Layout from './Layout.vue'

export default {
  extends: DefaultTheme,
  Layout
}
```

```vue [docs/.vitepress/theme/Layout.vue]
<script setup lang="ts">
import DefaultTheme from 'vitepress/theme'
import { useData, inBrowser } from 'vitepress'
import { watchEffect } from 'vue'

const { lang } = useData()
watchEffect(() => {
  if (inBrowser) {
    document.cookie = `nf_lang=${lang.value}; expires=Mon, 1 Jan 2030 00:00:00 UTC; path=/`
  }
})
</script>

<template>
  <DefaultTheme.Layout />
</template>
```

## Soporte a RTL (Experimental) {#rtl-support-experimental}

Para soporte a RTL (Right to Left), especifique `dir: 'rtl'` en la configuración y use algún plugin RTLCSS PostCSS como <https://github.com/MohammadYounes/rtlcss>, <https://github.com/vkalinichev/postcss-rtl> o <https://github.com/elchininet/postcss-rtlcss>. Necesitará configurar su plugin PostCSS para usar `:where([dir="ltr"])` y `:where([dir="rtl"])` como prefijos para evitar problemas de especificidad CSS.
