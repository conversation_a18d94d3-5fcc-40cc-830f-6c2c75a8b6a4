# Переход с VitePress 0.x

Если вы переходите с версии VitePress 0.x, то в ней есть несколько изменений, связанных с новыми функциями и улучшениями. Следуйте этому руководству, чтобы узнать, как перенести ваше приложение на последнюю версию VitePress.

## Конфигурация приложения

- Функция интернационализации ещё не реализована.

## Конфигурация темы

- Опция `sidebar` изменила свою структуру.
  - Ключ `children` теперь называется `items`.
  - Элемент верхнего уровня может не содержать `link` в данный момент. Мы планируем вернуть его обратно.
- `repo`, `repoLabel`, `docsDir`, `docsBranch`, `editLinks`, `editLinkText` удалены в пользу более гибкого api.
  - Для добавления ссылки GitHub с иконкой в навигацию используйте функцию [Социальные ссылки](../reference/default-theme-nav#navigation-links).
  - Для добавления ссылки «Редактировать эту страницу» используйте функцию [Ссылка для редактирования](../reference/default-theme-edit-link).
- Опция `lastUpdated` теперь разделена на `config.lastUpdated` и `themeConfig.lastUpdatedText`.
- Опция `carbonAds.carbon` заменена на `carbonAds.code`.

## Конфигурация метаданных

- Опция `home: true` заменена на `layout: home`. Кроме того, многие настройки, связанные с главной страницей, были изменены для обеспечения дополнительных возможностей. Подробности см. в разделе [Главная страница](../reference/default-theme-home-page).
- Опция `footer` перенесена в [`themeConfig.footer`](../reference/default-theme-config#footer).
