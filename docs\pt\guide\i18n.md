# Internacionalização {#internationalization}

Para usar recursos de i18n integrados, é necessário criar uma estrutura de diretórios da seguinte forma:

```
docs/
├─ es/
│  ├─ foo.md
├─ fr/
│  ├─ foo.md
├─ foo.md
```

Em seguida, no arquivo `docs/.vitepress/config.ts`:

```ts [docs/.vitepress/config.ts]
import { defineConfig } from 'vitepress'

export default defineConfig({
  // propriedades compartilhadas e outras coisas de nível superior...

  locales: {
    root: {
      label: 'English',
      lang: 'en'
    },
    fr: {
      label: 'French',
      lang: 'fr', // opcional, será adicionado como atributo `lang` na tag `html`
      link: '/fr/guide' // padrão /fr/ -- aparece no menu de traduções da barra de navegação, pode ser externo

      // outras propriedades específicas de cada idioma...
    }
  }
})
```

As seguintes propriedades podem ser substituídas para cada idioma (incluindo a raiz):

```ts
interface LocaleSpecificConfig<ThemeConfig = any> {
  lang?: string
  dir?: string
  title?: string
  titleTemplate?: string | boolean
  description?: string
  head?: HeadConfig[] // será mesclado com as entradas head existentes, as metatags duplicadas são removidas automaticamente
  themeConfig?: ThemeConfig // será mesclado superficialmente, coisas comuns podem ser colocadas na entrada de n[ivel superior de themeConfig
}
```

Consulte a interface [`DefaultTheme.Config`](https://github.com/vuejs/vitepress/blob/main/types/default-theme.d.ts) para obter detalhes sobre a personalização dos textos marcadores do tema padrão. Não substitua `themeConfig.algolia` ou `themeConfig.carbonAds` no nível do idioma. Consulte a [documentação Algolia](../reference/default-theme-search#i18n) para usar a pesquisa multilínguas.

**Dica profissional:** O arquivo de configuração pode ser armazenado em `docs/.vitepress/config/index.ts` também. Isso pode ajudar a organizar as coisas criando um arquivo de configuração por idioma e então mesclá-los e exportá-los a partir de `index.ts`.

## Diretório separado para cada localização {#separate-directory-for-each-locale}

A seguinte estrutura é totalmente válida:

```
docs/
├─ en/
│  ├─ foo.md
├─ es/
│  ├─ foo.md
├─ fr/
   ├─ foo.md
```

No entanto, VitePress não redirecionará `/` para `/en/` por padrão. Você precisará configurar seu servidor para isso. Por exemplo, no Netlify, você pode adicionar um arquivo `docs/public/_redirects` assim:

```
/*  /es/:splat  302  Language=es
/*  /fr/:splat  302  Language=fr
/*  /en/:splat  302
```

**Dica profissional:** Se estiver usando a abordagem acima, você pode usar o cookie `nf_lang` para persistir a escolha de idioma do usuário:

```ts [docs/.vitepress/theme/index.ts]
import DefaultTheme from 'vitepress/theme'
import Layout from './Layout.vue'

export default {
  extends: DefaultTheme,
  Layout
}
```

```vue [docs/.vitepress/theme/Layout.vue]
<script setup lang="ts">
import DefaultTheme from 'vitepress/theme'
import { useData, inBrowser } from 'vitepress'
import { watchEffect } from 'vue'

const { lang } = useData()
watchEffect(() => {
  if (inBrowser) {
    document.cookie = `nf_lang=${lang.value}; expires=Mon, 1 Jan 2030 00:00:00 UTC; path=/`
  }
})
</script>

<template>
  <DefaultTheme.Layout />
</template>
```

## Suporte a RTL (Experimental) {#rtl-support-experimental}

Para suporte a RTL (Right to Left), especifique `dir: 'rtl'` na configuração e use algum plugin RTLCSS PostCSS como <https://github.com/MohammadYounes/rtlcss>, <https://github.com/vkalinichev/postcss-rtl> ou <https://github.com/elchininet/postcss-rtlcss>. Você precisará configurar seu plugin PostCSS para usar `:where([dir="ltr"])` e `:where([dir="rtl"])` como prefixos para evitar problemas de especificidade CSS.
